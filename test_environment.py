#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本
"""

print("=== 图像拼接与布局优化系统 - 环境测试 ===")

# 测试基础库
print("\n1. 测试基础库导入...")
try:
    import sys
    print(f"✓ Python 版本: {sys.version}")
    
    import cv2
    print(f"✓ OpenCV 版本: {cv2.__version__}")
    
    import numpy as np
    print(f"✓ NumPy 版本: {np.__version__}")
    
    import matplotlib
    print(f"✓ Matplotlib 版本: {matplotlib.__version__}")
    
    from PIL import Image
    print(f"✓ Pillow 可用")
    
    import scipy
    print(f"✓ SciPy 版本: {scipy.__version__}")
    
    import skimage
    print(f"✓ Scikit-image 版本: {skimage.__version__}")
    
except ImportError as e:
    print(f"✗ 基础库导入失败: {e}")
    sys.exit(1)

# 测试项目模块
print("\n2. 测试项目模块导入...")
try:
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from src.image_processor import ImageProcessor
    print("✓ ImageProcessor 模块")
    
    from src.feature_matcher import FeatureMatcher
    print("✓ FeatureMatcher 模块")
    
    from src.image_stitcher import ImageStitcher
    print("✓ ImageStitcher 模块")
    
    from src.layout_optimizer import LayoutOptimizer
    print("✓ LayoutOptimizer 模块")
    
    from src.image_searcher import ImageSearcher
    print("✓ ImageSearcher 模块")
    
except ImportError as e:
    print(f"✗ 项目模块导入失败: {e}")
    print("请检查 src 目录是否存在以及模块文件是否完整")

# 测试基本功能
print("\n3. 测试基本功能...")
try:
    # 创建组件实例
    processor = ImageProcessor()
    print("✓ 图像处理器创建成功")
    
    matcher = FeatureMatcher()
    print("✓ 特征匹配器创建成功")
    
    stitcher = ImageStitcher()
    print("✓ 图像拼接器创建成功")
    
    optimizer = LayoutOptimizer()
    print("✓ 布局优化器创建成功")
    
    searcher = ImageSearcher()
    print("✓ 图像搜索器创建成功")
    
except Exception as e:
    print(f"✗ 组件创建失败: {e}")

print("\n=== 环境测试完成 ===")
print("如果所有测试都通过，您可以运行以下程序：")
print("- python main.py          # 启动GUI界面")
print("- python demo.py          # 运行功能演示")
print("- python quick_test.py    # 快速功能测试")
