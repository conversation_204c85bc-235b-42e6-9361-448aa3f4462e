#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单GUI测试
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

def test_imports():
    """测试导入"""
    try:
        import cv2
        import numpy as np
        import matplotlib
        from PIL import Image
        
        # 测试项目模块
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from src.image_processor import ImageProcessor
        
        messagebox.showinfo("测试结果", "所有模块导入成功！\n可以运行完整的图像拼接系统。")
        return True
    except Exception as e:
        messagebox.showerror("测试失败", f"模块导入失败：\n{str(e)}")
        return False

def run_demo():
    """运行演示"""
    try:
        import subprocess
        subprocess.Popen([sys.executable, "demo.py"], cwd=os.path.dirname(os.path.abspath(__file__)))
        messagebox.showinfo("启动", "演示程序已启动！")
    except Exception as e:
        messagebox.showerror("错误", f"启动演示失败：\n{str(e)}")

def run_main():
    """运行主程序"""
    try:
        import subprocess
        subprocess.Popen([sys.executable, "main.py"], cwd=os.path.dirname(os.path.abspath(__file__)))
        messagebox.showinfo("启动", "主程序已启动！")
    except Exception as e:
        messagebox.showerror("错误", f"启动主程序失败：\n{str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    root.title("图像拼接与布局优化系统 - 启动器")
    root.geometry("400x300")
    
    # 标题
    title_label = ttk.Label(root, text="图像拼接与布局优化系统", font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    # 说明
    info_label = ttk.Label(root, text="请选择要执行的操作：", font=("Arial", 12))
    info_label.pack(pady=10)
    
    # 按钮框架
    button_frame = ttk.Frame(root)
    button_frame.pack(pady=20)
    
    # 测试按钮
    test_btn = ttk.Button(button_frame, text="测试环境", command=test_imports, width=15)
    test_btn.pack(pady=5)
    
    # 演示按钮
    demo_btn = ttk.Button(button_frame, text="运行演示", command=run_demo, width=15)
    demo_btn.pack(pady=5)
    
    # 主程序按钮
    main_btn = ttk.Button(button_frame, text="启动主程序", command=run_main, width=15)
    main_btn.pack(pady=5)
    
    # 退出按钮
    exit_btn = ttk.Button(button_frame, text="退出", command=root.quit, width=15)
    exit_btn.pack(pady=5)
    
    # 状态信息
    status_label = ttk.Label(root, text="系统就绪", relief=tk.SUNKEN)
    status_label.pack(side=tk.BOTTOM, fill=tk.X)
    
    root.mainloop()

if __name__ == "__main__":
    main()
