#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像拼接与布局优化系统
主程序入口

作者: 算法分析期末项目
功能: 图像拼接、布局优化、相似图像搜索
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.image_processor import ImageProcessor
from src.feature_matcher import FeatureMatcher
from src.image_stitcher import ImageStitcher
from src.layout_optimizer import LayoutOptimizer
from src.image_searcher import ImageSearcher


class ImageStitchingApp:
    """图像拼接与布局优化系统主界面"""

    def __init__(self, root):
        self.root = root
        self.root.title("图像拼接与布局优化系统")
        self.root.geometry("1200x800")

        # 初始化组件
        self.image_processor = ImageProcessor()
        self.feature_matcher = FeatureMatcher()
        self.image_stitcher = ImageStitcher()
        self.layout_optimizer = LayoutOptimizer()
        self.image_searcher = ImageSearcher()

        # 存储图像数据
        self.images = []
        self.current_result = None

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)

        # 图像加载区域
        load_frame = ttk.LabelFrame(control_frame, text="图像加载")
        load_frame.pack(fill=tk.X, pady=5)

        ttk.Button(load_frame, text="选择图像", command=self.load_images).pack(pady=5)
        ttk.Button(load_frame, text="清空图像", command=self.clear_images).pack(pady=5)

        # 图像列表
        self.image_listbox = tk.Listbox(load_frame, height=6)
        self.image_listbox.pack(fill=tk.X, pady=5)

        # 功能选择区域
        function_frame = ttk.LabelFrame(control_frame, text="功能选择")
        function_frame.pack(fill=tk.X, pady=5)

        ttk.Button(function_frame, text="图像拼接", command=self.stitch_images).pack(fill=tk.X, pady=2)
        ttk.Button(function_frame, text="布局优化", command=self.optimize_layout).pack(fill=tk.X, pady=2)
        ttk.Button(function_frame, text="相似搜索", command=self.search_similar).pack(fill=tk.X, pady=2)

        # 参数设置区域
        params_frame = ttk.LabelFrame(control_frame, text="参数设置")
        params_frame.pack(fill=tk.X, pady=5)

        # 特征检测算法选择
        ttk.Label(params_frame, text="特征检测算法:").pack(anchor=tk.W)
        self.feature_var = tk.StringVar(value="SIFT")
        feature_combo = ttk.Combobox(params_frame, textvariable=self.feature_var,
                                   values=["SIFT", "ORB", "AKAZE"])
        feature_combo.pack(fill=tk.X, pady=2)

        # 匹配阈值
        ttk.Label(params_frame, text="匹配阈值:").pack(anchor=tk.W)
        self.threshold_var = tk.DoubleVar(value=0.7)
        threshold_scale = ttk.Scale(params_frame, from_=0.1, to=1.0,
                                  variable=self.threshold_var, orient=tk.HORIZONTAL)
        threshold_scale.pack(fill=tk.X, pady=2)

        # 右侧显示区域
        display_frame = ttk.LabelFrame(main_frame, text="结果显示")
        display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建画布用于显示图像
        self.canvas = tk.Canvas(display_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def load_images(self):
        """加载图像文件"""
        try:
            file_paths = filedialog.askopenfilenames(
                title="选择图像文件",
                filetypes=[
                    ("所有支持的图像", "*.jpg;*.jpeg;*.png;*.bmp;*.tiff;*.tif"),
                    ("JPEG文件", "*.jpg;*.jpeg"),
                    ("PNG文件", "*.png"),
                    ("BMP文件", "*.bmp"),
                    ("TIFF文件", "*.tiff;*.tif"),
                    ("所有文件", "*.*")
                ]
            )

            if not file_paths:
                self.status_var.set("未选择文件")
                return

            self.status_var.set("正在加载图像...")
            self.root.update()  # 强制更新界面

            loaded_count = 0
            for file_path in file_paths:
                try:
                    print(f"正在加载: {file_path}")  # 调试信息

                    # 检查文件是否存在
                    if not os.path.exists(file_path):
                        print(f"文件不存在: {file_path}")
                        continue

                    # 加载图像
                    image = self.image_processor.load_image(file_path)
                    if image is not None:
                        self.images.append({
                            'path': file_path,
                            'image': image,
                            'name': os.path.basename(file_path)
                        })
                        self.image_listbox.insert(tk.END, os.path.basename(file_path))
                        loaded_count += 1
                        print(f"成功加载: {os.path.basename(file_path)}")

                        # 显示第一张图像
                        if loaded_count == 1:
                            self.display_image(image)
                    else:
                        print(f"图像加载失败: {file_path}")
                        messagebox.showwarning("警告", f"无法加载图像: {os.path.basename(file_path)}")

                except Exception as e:
                    print(f"加载图像时出错: {str(e)}")
                    messagebox.showerror("错误", f"加载图像失败: {os.path.basename(file_path)}\n错误: {str(e)}")

            if loaded_count > 0:
                self.status_var.set(f"已加载 {loaded_count} 张图像")
            else:
                self.status_var.set("没有成功加载任何图像")

        except Exception as e:
            print(f"文件对话框错误: {str(e)}")
            messagebox.showerror("错误", f"打开文件对话框时出错: {str(e)}")
            self.status_var.set("加载失败")

    def clear_images(self):
        """清空已加载的图像"""
        self.images.clear()
        self.image_listbox.delete(0, tk.END)
        self.canvas.delete("all")
        self.current_result = None
        self.status_var.set("已清空图像")

    def stitch_images(self):
        """执行图像拼接"""
        if len(self.images) < 2:
            messagebox.showwarning("警告", "至少需要2张图像进行拼接")
            return

        try:
            self.status_var.set("正在进行图像拼接...")

            # 提取图像数组
            image_list = [img['image'] for img in self.images]

            # 设置参数
            self.feature_matcher.set_algorithm(self.feature_var.get())
            self.feature_matcher.set_threshold(self.threshold_var.get())

            # 执行拼接
            result = self.image_stitcher.stitch_images(image_list, self.feature_matcher)

            if result is not None:
                self.current_result = result
                self.display_image(result)
                self.status_var.set("图像拼接完成")
            else:
                messagebox.showerror("错误", "图像拼接失败")
                self.status_var.set("拼接失败")

        except Exception as e:
            messagebox.showerror("错误", f"拼接过程出错: {str(e)}")
            self.status_var.set("拼接出错")

    def optimize_layout(self):
        """执行布局优化"""
        if len(self.images) < 2:
            messagebox.showwarning("警告", "至少需要2张图像进行布局优化")
            return

        try:
            self.status_var.set("正在优化布局...")

            # 提取图像数组
            image_list = [img['image'] for img in self.images]

            # 执行布局优化
            result = self.layout_optimizer.optimize_layout(image_list)

            if result is not None:
                self.current_result = result
                self.display_image(result)
                self.status_var.set("布局优化完成")
            else:
                messagebox.showerror("错误", "布局优化失败")
                self.status_var.set("优化失败")

        except Exception as e:
            messagebox.showerror("错误", f"优化过程出错: {str(e)}")
            self.status_var.set("优化出错")

    def search_similar(self):
        """执行相似图像搜索"""
        if len(self.images) < 2:
            messagebox.showwarning("警告", "至少需要2张图像进行相似搜索")
            return

        try:
            self.status_var.set("正在搜索相似图像...")

            # 选择查询图像（使用第一张）
            query_image = self.images[0]['image']
            database_images = [img['image'] for img in self.images[1:]]

            # 执行搜索
            similarities = self.image_searcher.search_similar(query_image, database_images)

            # 显示结果
            self.show_search_results(similarities)
            self.status_var.set("相似搜索完成")

        except Exception as e:
            messagebox.showerror("错误", f"搜索过程出错: {str(e)}")
            self.status_var.set("搜索出错")

    def display_image(self, image):
        """在画布上显示图像"""
        # 转换为PIL图像
        if len(image.shape) == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image

        pil_image = Image.fromarray(image_rgb)

        # 调整图像大小以适应画布
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width > 1 and canvas_height > 1:
            pil_image.thumbnail((canvas_width-20, canvas_height-20), Image.Resampling.LANCZOS)

        # 转换为Tkinter图像
        self.photo = ImageTk.PhotoImage(pil_image)

        # 清空画布并显示图像
        self.canvas.delete("all")
        self.canvas.create_image(
            canvas_width//2, canvas_height//2,
            image=self.photo, anchor=tk.CENTER
        )

    def show_search_results(self, similarities):
        """显示搜索结果"""
        result_window = tk.Toplevel(self.root)
        result_window.title("相似图像搜索结果")
        result_window.geometry("600x400")

        # 创建结果列表
        result_frame = ttk.Frame(result_window)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(result_frame, text="相似度排序结果:", font=("Arial", 12, "bold")).pack(anchor=tk.W)

        result_listbox = tk.Listbox(result_frame)
        result_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        for i, similarity in enumerate(similarities):
            image_name = self.images[i+1]['name']  # +1因为第一张是查询图像
            result_listbox.insert(tk.END, f"{image_name} - 相似度: {similarity:.3f}")


def main():
    """主函数"""
    try:
        print("🚀 启动图像拼接与布局优化系统...")
        print("📋 检查环境...")

        # 检查依赖
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")

        import numpy as np
        print(f"✅ NumPy: {np.__version__}")

        from PIL import Image
        print("✅ Pillow: 可用")

        print("📦 加载系统模块...")

        # 测试模块导入
        from src.image_processor import ImageProcessor
        from src.feature_matcher import FeatureMatcher
        from src.image_stitcher import ImageStitcher
        from src.layout_optimizer import LayoutOptimizer
        from src.image_searcher import ImageSearcher

        print("✅ 所有模块加载成功")
        print("🖥️  启动GUI界面...")

        root = tk.Tk()

        # 设置窗口图标和属性
        root.title("图像拼接与布局优化系统 v1.0")
        root.geometry("1200x800")
        root.minsize(800, 600)

        # 居中显示窗口
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (root.winfo_screenheight() // 2) - (800 // 2)
        root.geometry(f"1200x800+{x}+{y}")

        app = ImageStitchingApp(root)

        print("✅ GUI界面启动成功!")
        print("📖 使用说明:")
        print("   1. 点击'选择图像'加载图像文件")
        print("   2. 选择特征检测算法和参数")
        print("   3. 点击功能按钮执行相应操作")
        print("   4. 在右侧查看处理结果")

        root.mainloop()

    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保已安装所有依赖包:")
        print("   pip install opencv-python numpy matplotlib Pillow scikit-image scipy")
        input("按回车键退出...")

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")


if __name__ == "__main__":
    main()
